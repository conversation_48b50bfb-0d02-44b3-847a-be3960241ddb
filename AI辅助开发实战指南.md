# AI辅助开发实战指南
## 3个月完成药帮忙App重构的AI工具使用手册

---

## 🤖 AI工具配置清单

### 必备AI工具
```
✅ GitHub Copilot ($10/月)
   - 实时代码补全
   - 函数生成
   - 注释生成

✅ ChatGPT Plus ($20/月)
   - 架构设计
   - 问题解决
   - 代码审查

✅ Cursor IDE (免费/付费)
   - AI原生开发环境
   - 智能代码编辑
   - 项目级AI理解

✅ v0.dev (免费)
   - UI组件快速生成
   - 响应式设计
   - 现代化界面
```

### 辅助AI工具
```
🔧 Claude (备用方案)
📝 Notion AI (文档生成)
🎨 Midjourney (图标设计)
🧪 GitHub Actions (CI/CD自动化)
```

---

## 📋 第一周详细执行计划

### Day 1: 环境搭建
```bash
# 1. 安装Cursor IDE
curl -fsSL https://download.cursor.sh/linux/appImage/x64 -o cursor.appimage

# 2. 配置GitHub Copilot
# 在Cursor中安装GitHub Copilot插件
# 登录GitHub账号并激活订阅

# 3. 创建uniappX项目
npx @dcloudio/uvm@latest create my-project
cd my-project
npm install

# 4. 安装开发依赖
npm install -D @types/node typescript eslint prettier
npm install uview-plus pinia @pinia/nuxt
```

### Day 2: AI辅助项目架构
```typescript
// 使用AI生成项目结构
// Prompt: "请为uniappX电商项目设计目录结构和基础配置"

src/
├── components/          // 公共组件
│   ├── common/         // 通用组件
│   └── business/       // 业务组件
├── pages/              // 页面
│   ├── index/         // 首页
│   ├── product/       // 商品
│   ├── cart/          // 购物车
│   ├── order/         // 订单
│   └── user/          // 用户
├── store/              // 状态管理
├── utils/              // 工具函数
├── types/              // 类型定义
└── static/             // 静态资源
```

### Day 3-4: 基础组件开发

#### AI生成网络请求封装
```typescript
// Prompt: "创建uniappX的网络请求封装，支持拦截器、错误处理、loading"

// utils/request.ts (AI生成 + 人工优化)
interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  showLoading?: boolean
}

class HttpRequest {
  private baseURL = 'https://api.ybm100.com'
  
  async request<T>(config: RequestConfig): Promise<T> {
    if (config.showLoading) {
      uni.showLoading({ title: '加载中...' })
    }
    
    try {
      const response = await uni.request({
        url: this.baseURL + config.url,
        method: config.method || 'GET',
        data: config.data,
        header: {
          'Authorization': uni.getStorageSync('token'),
          'Content-Type': 'application/json'
        }
      })
      
      return response.data
    } catch (error) {
      uni.showToast({ title: '网络错误', icon: 'error' })
      throw error
    } finally {
      if (config.showLoading) {
        uni.hideLoading()
      }
    }
  }
}

export const http = new HttpRequest()
```

#### AI生成状态管理
```typescript
// Prompt: "使用Pinia创建用户状态管理，包含登录、用户信息、购物车"

// store/user.ts (AI生成)
import { defineStore } from 'pinia'

interface UserInfo {
  id: string
  name: string
  phone: string
  avatar: string
}

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null as UserInfo | null,
    token: '',
    isLogin: false
  }),
  
  actions: {
    async login(phone: string, password: string) {
      const response = await http.post('/auth/login', { phone, password })
      this.token = response.token
      this.userInfo = response.userInfo
      this.isLogin = true
      uni.setStorageSync('token', this.token)
    },
    
    logout() {
      this.token = ''
      this.userInfo = null
      this.isLogin = false
      uni.removeStorageSync('token')
    }
  }
})
```

### Day 5: 公共组件库

#### AI生成商品卡片组件
```vue
<!-- Prompt: "创建商品卡片组件，包含图片、标题、价格、标签、点击事件" -->

<!-- components/business/ProductCard.vue -->
<template>
  <view class="product-card" @click="handleClick">
    <image :src="product.image" class="product-image" />
    <view class="product-info">
      <text class="product-name">{{ product.name }}</text>
      <view class="product-tags">
        <text 
          v-for="tag in product.tags" 
          :key="tag"
          class="tag"
        >
          {{ tag }}
        </text>
      </view>
      <view class="product-price">
        <text class="price">¥{{ product.price }}</text>
        <text v-if="product.originalPrice" class="original-price">
          ¥{{ product.originalPrice }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
interface Product {
  id: string
  name: string
  price: number
  originalPrice?: number
  image: string
  tags: string[]
}

interface Props {
  product: Product
}

const props = defineProps<Props>()
const emit = defineEmits<{
  click: [product: Product]
}>()

const handleClick = () => {
  emit('click', props.product)
}
</script>

<style scoped>
.product-card {
  background: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.product-image {
  width: 100%;
  height: 200px;
  border-radius: 6px;
}

.product-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-top: 8px;
}

.product-tags {
  display: flex;
  gap: 4px;
  margin: 6px 0;
}

.tag {
  background: #f0f0f0;
  color: #666;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price {
  color: #ff4757;
  font-size: 18px;
  font-weight: bold;
}

.original-price {
  color: #999;
  font-size: 14px;
  text-decoration: line-through;
}
</style>
```

---

## 🎯 AI Prompt模板库

### 页面生成Prompt
```
角色: 你是一个资深的uniappX开发专家
任务: 创建${页面名称}页面
要求:
1. 使用Vue3 Composition API + TypeScript
2. 集成uView Plus组件库
3. 包含${具体功能列表}
4. 响应式设计，适配手机和平板
5. 添加loading状态和错误处理
6. 遵循药帮忙App的设计规范
7. 包含必要的SEO优化
背景: 这是一个医药电商App
输出: 请提供完整的.vue文件代码
```

### 组件生成Prompt
```
请创建一个${组件名称}组件，要求:

功能需求:
- ${功能1}
- ${功能2}
- ${功能3}

技术要求:
- Vue3 + TypeScript
- Props类型定义
- 事件发射
- 插槽支持
- 样式隔离
- 响应式设计

设计要求:
- 现代化UI设计
- 符合医药行业特色
- 支持主题切换
- 无障碍访问支持

请提供完整的组件代码，包含template、script、style三部分
```

### 业务逻辑Prompt
```
场景: 药帮忙电商App的${业务场景}
需求: 实现${具体业务需求}

技术栈:
- uniappX + Vue3
- Pinia状态管理
- TypeScript

业务规则:
- ${规则1}
- ${规则2}
- ${规则3}

请提供:
1. 业务逻辑代码
2. 状态管理代码
3. 类型定义
4. 错误处理
5. 单元测试用例
```

---

## 📊 AI辅助开发效率监控

### 每日效率统计
```typescript
// 效率监控脚本 (AI生成)
interface DailyStats {
  date: string
  linesOfCode: number
  aiGeneratedLines: number
  componentsCreated: number
  bugsFixed: number
  timeSpent: number
}

class EfficiencyTracker {
  private stats: DailyStats[] = []
  
  addDailyStats(stats: DailyStats) {
    this.stats.push(stats)
  }
  
  getEfficiencyReport() {
    const totalLines = this.stats.reduce((sum, day) => sum + day.linesOfCode, 0)
    const aiLines = this.stats.reduce((sum, day) => sum + day.aiGeneratedLines, 0)
    const aiPercentage = (aiLines / totalLines) * 100
    
    return {
      totalLines,
      aiPercentage,
      averageDaily: totalLines / this.stats.length,
      efficiency: aiPercentage > 60 ? 'High' : 'Medium'
    }
  }
}
```

### 代码质量检查
```bash
# AI辅助代码质量检查脚本
#!/bin/bash

echo "🤖 AI辅助代码质量检查开始..."

# ESLint检查
npx eslint src/ --ext .vue,.ts,.js

# TypeScript类型检查
npx vue-tsc --noEmit

# 代码复杂度分析
npx complexity-report src/

# AI代码审查 (使用ChatGPT API)
node scripts/ai-code-review.js

echo "✅ 代码质量检查完成"
```

---

## 🔧 AI工具使用技巧

### GitHub Copilot最佳实践
```typescript
// 1. 写清晰的注释，Copilot会根据注释生成代码
// 创建一个商品搜索函数，支持关键词、分类、价格区间筛选
async function searchProducts(params: SearchParams): Promise<Product[]> {
  // Copilot会自动补全函数实现
}

// 2. 使用描述性的变量名
const userShoppingCartItems = [] // Copilot理解这是购物车商品
const productRecommendationList = [] // Copilot理解这是推荐商品

// 3. 分步骤写代码，让Copilot理解上下文
// 第一步：验证用户输入
// 第二步：调用API获取数据  
// 第三步：处理返回结果
```

### ChatGPT使用策略
```
🎯 架构设计: 先让ChatGPT设计整体架构，再细化具体实现
🔍 问题解决: 遇到bug时，提供错误信息让ChatGPT分析原因
📝 代码优化: 让ChatGPT审查代码并提供优化建议
🧪 测试生成: 让ChatGPT生成测试用例和测试数据
📚 文档编写: 让ChatGPT生成API文档和使用说明
```

### Cursor IDE技巧
```
💡 快捷键:
- Ctrl+K: 打开AI聊天
- Ctrl+L: 选中代码进行AI编辑
- Ctrl+I: AI代码补全

🎯 使用技巧:
- 选中代码块，让AI解释或优化
- 在注释中描述需求，AI自动生成代码
- 使用AI重构功能优化代码结构
```

---

## 📈 进度跟踪表

### 第一个月里程碑
| 周次 | 目标 | AI辅助重点 | 完成标准 |
|------|------|------------|----------|
| Week 1 | 项目搭建 | 架构生成 | ✅ 基础框架运行 |
| Week 2 | 首页+商品 | 页面生成 | ✅ 核心页面完成 |
| Week 3 | 购物车+用户 | 状态管理 | ✅ 用户流程打通 |
| Week 4 | 订单模块 | 业务逻辑 | ✅ 下单流程完成 |

### 每日任务模板
```
📅 日期: ${date}
🎯 今日目标: ${goals}
🤖 AI工具使用:
  - GitHub Copilot: ${usage}
  - ChatGPT: ${usage}
  - Cursor: ${usage}

📊 完成情况:
  - 代码行数: ${lines}
  - AI生成比例: ${percentage}
  - 组件完成: ${components}
  - 问题解决: ${issues}

🔄 明日计划: ${tomorrow_plan}
```

---

## 🚨 常见问题解决

### AI工具使用问题
```
Q: GitHub Copilot建议的代码有错误怎么办？
A: 1. 检查上下文是否清晰
   2. 修改注释让AI理解需求
   3. 人工审查和修正
   4. 使用ChatGPT进行二次验证

Q: ChatGPT生成的代码不符合项目规范？
A: 1. 在Prompt中明确项目规范
   2. 提供代码示例作为参考
   3. 使用ESLint自动格式化
   4. 建立项目专用Prompt模板

Q: AI生成的代码性能不好？
A: 1. 让AI分析性能瓶颈
   2. 要求AI提供优化方案
   3. 使用性能分析工具验证
   4. 人工review关键代码
```

### 开发效率问题
```
Q: 如何确保3个月内完成？
A: 1. 严格按照周计划执行
   2. 每日进度跟踪和调整
   3. 优先完成核心功能
   4. 使用AI工具最大化效率

Q: 2人团队如何协作？
A: 1. 明确分工和接口定义
   2. 使用Git进行版本控制
   3. 每日站会同步进度
   4. AI辅助代码审查
```

---

## 🎉 成功验收标准

### 技术指标
- [ ] **代码质量**: ESLint通过率 > 95%
- [ ] **AI使用率**: AI生成代码占比 > 60%
- [ ] **性能指标**: 页面加载时间 < 2秒
- [ ] **测试覆盖**: 核心功能测试覆盖率 > 80%

### 功能指标
- [ ] **页面完成**: 334个页面全部实现
- [ ] **业务流程**: 核心业务流程完整
- [ ] **第三方集成**: 支付、地图等集成完成
- [ ] **用户体验**: 界面现代化，交互流畅

### 项目指标
- [ ] **时间控制**: 3个月内完成
- [ ] **成本控制**: 不超过12.4万预算
- [ ] **团队效率**: 开发效率提升4倍
- [ ] **质量保证**: 核心功能零重大bug

---

**准备好开始这个激动人心的AI辅助开发之旅了吗？让我们用AI的力量在3个月内完成这个挑战！** 🚀
