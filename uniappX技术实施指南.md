# uniappX技术实施指南
## 药帮忙App重构技术手册

---

## 📋 快速开始检查清单

### 第一周任务清单
- [ ] 安装HBuilderX开发环境
- [ ] 创建uniappX项目模板
- [ ] 配置TypeScript开发环境
- [ ] 搭建基础项目架构
- [ ] 完成第一个示例页面

### 环境准备
```bash
# 1. 下载HBuilderX
# 访问: https://www.dcloud.io/hbuilderx.html

# 2. 安装Node.js (推荐v16+)
node --version

# 3. 安装Vue CLI
npm install -g @vue/cli

# 4. 创建uniappX项目
vue create -p dcloudio/uni-preset-vue#vite-ts my-project
```

---

## 🏗️ 项目架构模板

### 目录结构
```
src/
├── pages/                 # 页面文件
│   ├── index/             # 首页模块
│   ├── product/           # 商品模块
│   ├── order/             # 订单模块
│   └── user/              # 用户模块
├── components/            # 公共组件
│   ├── common/            # 通用组件
│   └── business/          # 业务组件
├── store/                 # 状态管理
│   ├── modules/           # 模块化store
│   └── index.ts           # store入口
├── utils/                 # 工具函数
│   ├── request.ts         # 网络请求
│   ├── storage.ts         # 本地存储
│   └── common.ts          # 通用工具
├── types/                 # TypeScript类型定义
├── static/                # 静态资源
└── App.vue               # 应用入口
```

### 基础配置文件

#### manifest.json 配置
```json
{
  "name": "药帮忙",
  "appid": "__UNI__XXXXXX",
  "description": "医药电商平台",
  "versionName": "2.0.0",
  "versionCode": "200",
  "transformPx": false,
  "app-plus": {
    "usingComponents": true,
    "nvueStyleCompiler": "uni-app",
    "compilerVersion": 3,
    "splashscreen": {
      "alwaysShowBeforeRender": true,
      "waiting": true,
      "autoclose": true,
      "delay": 0
    }
  }
}
```

#### pages.json 配置
```json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "药帮忙",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#3cc51f",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "static/tab_home.png",
        "selectedIconPath": "static/tab_home_active.png",
        "text": "首页"
      }
    ]
  }
}
```

---

## 🔧 核心功能实现

### 网络请求封装
```typescript
// utils/request.ts
interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  header?: any
}

interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

class HttpRequest {
  private baseURL = 'https://api.ybm100.com'
  private timeout = 10000

  async request<T>(config: RequestConfig): Promise<ApiResponse<T>> {
    return new Promise((resolve, reject) => {
      uni.request({
        url: this.baseURL + config.url,
        method: config.method || 'GET',
        data: config.data,
        header: {
          'Content-Type': 'application/json',
          'Authorization': uni.getStorageSync('token'),
          ...config.header
        },
        timeout: this.timeout,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data as ApiResponse<T>)
          } else {
            reject(new Error(`请求失败: ${res.statusCode}`))
          }
        },
        fail: reject
      })
    })
  }

  get<T>(url: string, data?: any) {
    return this.request<T>({ url, method: 'GET', data })
  }

  post<T>(url: string, data?: any) {
    return this.request<T>({ url, method: 'POST', data })
  }
}

export const http = new HttpRequest()
```

### 状态管理实现
```typescript
// store/modules/user.ts
import { defineStore } from 'pinia'

interface UserInfo {
  id: string
  name: string
  phone: string
  avatar: string
}

interface UserState {
  userInfo: UserInfo | null
  token: string
  isLogin: boolean
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    userInfo: null,
    token: uni.getStorageSync('token') || '',
    isLogin: false
  }),

  getters: {
    userName: (state) => state.userInfo?.name || '未登录',
    isAuthenticated: (state) => !!state.token && state.isLogin
  },

  actions: {
    async login(phone: string, password: string) {
      try {
        const response = await http.post('/auth/login', { phone, password })
        
        if (response.code === 200) {
          this.token = response.data.token
          this.userInfo = response.data.userInfo
          this.isLogin = true
          
          // 持久化存储
          uni.setStorageSync('token', this.token)
          uni.setStorageSync('userInfo', this.userInfo)
          
          return { success: true }
        } else {
          throw new Error(response.message)
        }
      } catch (error) {
        return { success: false, message: error.message }
      }
    },

    logout() {
      this.token = ''
      this.userInfo = null
      this.isLogin = false
      
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      
      uni.reLaunch({ url: '/pages/login/login' })
    }
  }
})
```

### 公共组件示例
```vue
<!-- components/common/LoadingButton.vue -->
<template>
  <button 
    class="loading-button"
    :class="{ 'loading': loading, 'disabled': disabled }"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <view v-if="loading" class="loading-icon">⟳</view>
    <text>{{ loading ? loadingText : text }}</text>
  </button>
</template>

<script setup lang="ts">
interface Props {
  text: string
  loadingText?: string
  loading?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loadingText: '加载中...',
  loading: false,
  disabled: false
})

const emit = defineEmits<{
  click: []
}>()

const handleClick = () => {
  if (!props.loading && !props.disabled) {
    emit('click')
  }
}
</script>

<style scoped>
.loading-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
}

.loading-button.loading {
  opacity: 0.7;
}

.loading-button.disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.loading-icon {
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
```

---

## 📱 页面开发模板

### 商品列表页示例
```vue
<!-- pages/product/list.vue -->
<template>
  <view class="product-list">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <input 
        v-model="searchKeyword"
        placeholder="搜索商品"
        @confirm="handleSearch"
      />
    </view>

    <!-- 商品列表 -->
    <scroll-view 
      scroll-y 
      class="product-scroll"
      @scrolltolower="loadMore"
    >
      <view 
        v-for="product in productList" 
        :key="product.id"
        class="product-item"
        @click="goToDetail(product.id)"
      >
        <image :src="product.image" class="product-image" />
        <view class="product-info">
          <text class="product-name">{{ product.name }}</text>
          <text class="product-price">¥{{ product.price }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 加载更多 -->
    <LoadingButton 
      v-if="hasMore"
      :loading="loading"
      text="加载更多"
      @click="loadMore"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { http } from '@/utils/request'
import LoadingButton from '@/components/common/LoadingButton.vue'

interface Product {
  id: string
  name: string
  price: number
  image: string
}

const searchKeyword = ref('')
const productList = ref<Product[]>([])
const loading = ref(false)
const hasMore = ref(true)
const page = ref(1)

const loadProducts = async (isLoadMore = false) => {
  if (loading.value) return
  
  loading.value = true
  
  try {
    const response = await http.get('/products', {
      keyword: searchKeyword.value,
      page: isLoadMore ? page.value : 1,
      pageSize: 20
    })
    
    if (response.code === 200) {
      if (isLoadMore) {
        productList.value.push(...response.data.list)
      } else {
        productList.value = response.data.list
      }
      
      hasMore.value = response.data.hasMore
      if (isLoadMore) page.value++
    }
  } catch (error) {
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  page.value = 1
  loadProducts()
}

const loadMore = () => {
  if (hasMore.value) {
    loadProducts(true)
  }
}

const goToDetail = (productId: string) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${productId}`
  })
}

onMounted(() => {
  loadProducts()
})
</script>

<style scoped>
.product-list {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.search-bar {
  padding: 12px;
  background-color: #f5f5f5;
}

.search-bar input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

.product-scroll {
  flex: 1;
  padding: 12px;
}

.product-item {
  display: flex;
  padding: 12px;
  margin-bottom: 12px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 4px;
  margin-right: 12px;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 16px;
  color: #333;
  line-height: 1.4;
}

.product-price {
  font-size: 18px;
  color: #ff4757;
  font-weight: bold;
}
</style>
```

---

## 🔌 第三方集成示例

### 支付功能集成
```typescript
// utils/payment.ts
interface PaymentParams {
  orderId: string
  amount: number
  title: string
}

class PaymentService {
  async wechatPay(params: PaymentParams) {
    try {
      // 获取支付参数
      const response = await http.post('/payment/wechat/prepare', params)
      
      if (response.code === 200) {
        const paymentData = response.data
        
        return new Promise((resolve, reject) => {
          uni.requestPayment({
            provider: 'wxpay',
            timeStamp: paymentData.timeStamp,
            nonceStr: paymentData.nonceStr,
            package: paymentData.package,
            signType: paymentData.signType,
            paySign: paymentData.paySign,
            success: resolve,
            fail: reject
          })
        })
      }
    } catch (error) {
      throw new Error('支付失败: ' + error.message)
    }
  }

  async alipay(params: PaymentParams) {
    try {
      const response = await http.post('/payment/alipay/prepare', params)
      
      if (response.code === 200) {
        return new Promise((resolve, reject) => {
          uni.requestPayment({
            provider: 'alipay',
            orderInfo: response.data.orderInfo,
            success: resolve,
            fail: reject
          })
        })
      }
    } catch (error) {
      throw new Error('支付失败: ' + error.message)
    }
  }
}

export const paymentService = new PaymentService()
```

---

## 🚀 部署和发布

### 打包配置
```javascript
// vue.config.js
module.exports = {
  transpileDependencies: ['@dcloudio/uni-ui'],
  configureWebpack: {
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            chunks: 'all',
            priority: 10
          }
        }
      }
    }
  }
}
```

### 发布流程
```bash
# 1. 构建生产版本
npm run build:app-plus

# 2. 生成安装包
# 在HBuilderX中: 发行 -> 原生App-云打包

# 3. 测试安装包
# 安装到测试设备进行功能验证

# 4. 上传应用商店
# 按照各应用商店要求上传审核
```

---

## 📞 技术支持

### 常见问题解决
1. **编译错误**: 检查TypeScript类型定义
2. **页面白屏**: 检查路由配置和页面路径
3. **网络请求失败**: 检查域名白名单配置
4. **第三方插件问题**: 查看插件文档和兼容性

### 学习资源
- [uniappX官方文档](https://uniapp.dcloud.net.cn/)
- [Vue3官方文档](https://v3.vuejs.org/)
- [TypeScript文档](https://www.typescriptlang.org/)

### 技术交流
- 项目技术群: [群号]
- 技术分享会: 每周五下午
- 代码审查: 每次提交必须审查
